# VMware Control & Evasion - Implementation Guide

## Project Structure

```
VMwareController/
├── src/
│   ├── core/
│   │   ├── VMwareEngine.cpp/h      # Main VMware control logic
│   │   ├── EvasionEngine.cpp/h     # Detection evasion core
│   │   └── ConfigManager.cpp/h     # Configuration management
│   ├── drivers/
│   │   ├── FileSystemFilter/       # File system filter driver
│   │   └── RegistryFilter/         # Registry filter driver
│   ├── ui/
│   │   ├── MainWindow.cpp/h        # Primary user interface
│   │   └── ConfigDialog.cpp/h      # Configuration dialogs
│   ├── utils/
│   │   ├── SystemUtils.cpp/h       # System utility functions
│   │   └── VMwareUtils.cpp/h       # VMware-specific utilities
│   └── services/
│       └── VMControlService.cpp/h  # Windows service component
├── include/
│   ├── vmware/                     # VMware API headers
│   └── windows/                    # Windows-specific headers
├── lib/                            # Third-party libraries
├── config/                         # Configuration files
└── docs/                          # Documentation
```

## Core Implementation Components

### 1. VMware Control Engine

#### Basic VM Operations
```cpp
// VMwareEngine.h
class VMwareEngine {
private:
    VixHandle hostHandle;
    std::map<std::string, VixHandle> vmHandles;
    
public:
    bool Initialize();
    bool ConnectToHost();
    std::vector<VMInfo> DiscoverVMs();
    bool StartVM(const std::string& vmxPath);
    bool StopVM(const std::string& vmxPath);
    bool PauseVM(const std::string& vmxPath);
    bool ResumeVM(const std::string& vmxPath);
    VMStatus GetVMStatus(const std::string& vmxPath);
    bool ModifyVMConfig(const std::string& vmxPath, const ConfigChanges& changes);
};
```

#### VM Configuration Management
```cpp
// ConfigManager.h
class ConfigManager {
public:
    struct VMXConfig {
        std::map<std::string, std::string> settings;
        bool LoadFromFile(const std::string& vmxPath);
        bool SaveToFile(const std::string& vmxPath);
        void ApplyEvasionSettings();
    };
    
    bool ModifyVMXFile(const std::string& vmxPath, const std::map<std::string, std::string>& changes);
    bool ApplyAntiDetectionConfig(const std::string& vmxPath);
};
```

### 2. Detection Evasion Engine

#### Registry Manipulation
```cpp
// EvasionEngine.h
class RegistryEvasion {
private:
    std::vector<std::string> vmwareRegistryKeys;
    std::map<std::string, std::string> originalValues;
    
public:
    bool HideVMwareRegistry();
    bool RestoreRegistry();
    bool ModifyRegistryValue(const std::string& keyPath, const std::string& valueName, const std::string& newValue);
    bool DeleteRegistryKey(const std::string& keyPath);
};
```

#### File System Masking
```cpp
class FileSystemEvasion {
private:
    std::vector<std::string> vmwareFiles;
    std::vector<std::string> vmwareDirectories;
    
public:
    bool HideVMwareFiles();
    bool MaskVMwareProcesses();
    bool InstallFileSystemFilter();
    bool UninstallFileSystemFilter();
};
```

#### Hardware Spoofing
```cpp
class HardwareSpoofer {
public:
    bool SpoofCPUID();
    bool ModifySystemInfo();
    bool ChangeMACAddress(const std::string& adapterName, const std::string& newMAC);
    bool SpoofHardwareIDs();
    bool ModifyBIOSInfo();
};
```

## Specific Evasion Techniques

### 1. Common VM Detection Points

#### Registry Keys to Modify/Hide
```cpp
const std::vector<std::string> VMWARE_REGISTRY_KEYS = {
    "HKEY_LOCAL_MACHINE\\SOFTWARE\\VMware, Inc.\\VMware Tools",
    "HKEY_LOCAL_MACHINE\\SYSTEM\\ControlSet001\\Services\\vmdebug",
    "HKEY_LOCAL_MACHINE\\SYSTEM\\ControlSet001\\Services\\vmmouse",
    "HKEY_LOCAL_MACHINE\\SYSTEM\\ControlSet001\\Services\\VMTools",
    "HKEY_LOCAL_MACHINE\\SYSTEM\\ControlSet001\\Services\\VMMEMCTL",
    "HKEY_LOCAL_MACHINE\\SYSTEM\\ControlSet001\\Services\\vmware",
    "HKEY_LOCAL_MACHINE\\SYSTEM\\ControlSet001\\Services\\vmci",
    "HKEY_LOCAL_MACHINE\\SYSTEM\\ControlSet001\\Services\\vmx86"
};
```

#### Files and Directories to Hide
```cpp
const std::vector<std::string> VMWARE_FILES = {
    "C:\\Program Files\\VMware\\VMware Tools\\",
    "C:\\Windows\\System32\\drivers\\vmmouse.sys",
    "C:\\Windows\\System32\\drivers\\vmhgfs.sys",
    "C:\\Windows\\System32\\drivers\\vmxnet.sys",
    "C:\\Windows\\System32\\drivers\\vmci.sys",
    "C:\\Windows\\System32\\drivers\\vmx86.sys"
};
```

#### Processes to Hide
```cpp
const std::vector<std::string> VMWARE_PROCESSES = {
    "vmtoolsd.exe",
    "vmwaretray.exe",
    "vmwareuser.exe",
    "VGAuthService.exe",
    "vmacthlp.exe"
};
```

### 2. Hardware Identifier Spoofing

#### CPUID Modification
```cpp
bool HardwareSpoofer::SpoofCPUID() {
    // Hook CPUID instruction to return modified values
    // This requires kernel-level implementation
    
    // Example: Modify hypervisor bit in CPUID leaf 1
    // Original VMware returns specific values that can be detected
    
    return InstallCPUIDHook();
}
```

#### System Information Spoofing
```cpp
bool HardwareSpoofer::ModifySystemInfo() {
    // Modify WMI queries that return VM-specific information
    struct SystemInfo {
        std::string manufacturer = "Dell Inc.";  // Instead of "VMware, Inc."
        std::string model = "OptiPlex 7090";     // Instead of "VMware Virtual Platform"
        std::string biosVersion = "2.18.0";     // Instead of VMware BIOS version
    };
    
    return ApplySystemInfoChanges();
}
```

### 3. Network Adapter Management

#### MAC Address Spoofing
```cpp
bool HardwareSpoofer::ChangeMACAddress(const std::string& adapterName, const std::string& newMAC) {
    // Change MAC address to avoid VMware OUI detection
    // VMware uses OUI: 00:0C:29, 00:50:56, 00:1C:14
    
    std::string registryPath = "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{4D36E972-E325-11CE-BFC1-08002BE10318}";
    
    // Find adapter and modify NetworkAddress value
    return ModifyNetworkAdapterRegistry(adapterName, newMAC);
}
```

## Advanced Implementation Techniques

### 1. Kernel Driver Development

#### File System Filter Driver
```cpp
// FileSystemFilter.c (Kernel mode)
NTSTATUS FilterCreate(PFLT_CALLBACK_DATA Data, PCFLT_RELATED_OBJECTS FltObjects, PVOID* CompletionContext) {
    PFLT_FILE_NAME_INFORMATION nameInfo;
    NTSTATUS status = FltGetFileNameInformation(Data, FLT_FILE_NAME_NORMALIZED, &nameInfo);
    
    if (NT_SUCCESS(status)) {
        // Check if file path contains VMware-related strings
        if (IsVMwareRelatedPath(nameInfo->Name.Buffer)) {
            // Block or redirect the file access
            Data->IoStatus.Status = STATUS_FILE_NOT_FOUND;
            Data->IoStatus.Information = 0;
            return FLT_PREOP_COMPLETE;
        }
    }
    
    return FLT_PREOP_SUCCESS_NO_CALLBACK;
}
```

#### Registry Filter Driver
```cpp
// RegistryFilter.c (Kernel mode)
NTSTATUS RegistryCallback(PVOID CallbackContext, PVOID Argument1, PVOID Argument2) {
    REG_NOTIFY_CLASS notifyClass = (REG_NOTIFY_CLASS)(ULONG_PTR)Argument1;
    
    if (notifyClass == RegNtPreQueryValueKey) {
        PREG_QUERY_VALUE_KEY_INFORMATION queryInfo = (PREG_QUERY_VALUE_KEY_INFORMATION)Argument2;
        
        // Check if querying VMware-related registry values
        if (IsVMwareRegistryQuery(queryInfo)) {
            // Block or modify the query
            return STATUS_OBJECT_NAME_NOT_FOUND;
        }
    }
    
    return STATUS_SUCCESS;
}
```

### 2. API Hooking Implementation

#### User-Mode API Hooking
```cpp
// APIHooking.cpp
class APIHooker {
private:
    std::map<std::string, FARPROC> originalFunctions;
    
public:
    bool HookFunction(const std::string& moduleName, const std::string& functionName, FARPROC hookFunction);
    bool UnhookFunction(const std::string& moduleName, const std::string& functionName);
};

// Example: Hook RegQueryValueEx to hide VMware registry entries
LONG WINAPI HookedRegQueryValueEx(HKEY hKey, LPCSTR lpValueName, LPDWORD lpReserved, 
                                  LPDWORD lpType, LPBYTE lpData, LPDWORD lpcbData) {
    
    if (IsVMwareRegistryValue(lpValueName)) {
        return ERROR_FILE_NOT_FOUND;
    }
    
    // Call original function
    return OriginalRegQueryValueEx(hKey, lpValueName, lpReserved, lpType, lpData, lpcbData);
}
```

### 3. Memory Patching Techniques

#### Runtime Memory Modification
```cpp
class MemoryPatcher {
public:
    bool PatchProcessMemory(DWORD processId, LPVOID address, const std::vector<BYTE>& newBytes);
    bool FindAndPatchPattern(DWORD processId, const std::vector<BYTE>& pattern, const std::vector<BYTE>& replacement);
    bool InjectDLL(DWORD processId, const std::string& dllPath);
};
```

## Configuration and Profiles

### Evasion Profiles
```json
{
  "profiles": {
    "stealth_mode": {
      "registry_evasion": true,
      "file_system_masking": true,
      "process_hiding": true,
      "hardware_spoofing": true,
      "network_spoofing": true,
      "custom_hardware_info": {
        "manufacturer": "Dell Inc.",
        "model": "OptiPlex 7090",
        "bios_version": "2.18.0"
      }
    },
    "minimal_evasion": {
      "registry_evasion": true,
      "file_system_masking": false,
      "process_hiding": true,
      "hardware_spoofing": false,
      "network_spoofing": true
    }
  }
}
```

## Security Considerations

### 1. Code Signing
- All drivers must be properly signed for Windows 10/11
- Consider using cross-signing certificates
- Implement proper certificate management

### 2. Anti-Analysis Protection
- Code obfuscation for sensitive components
- Anti-debugging techniques
- Runtime packing/encryption

### 3. Privilege Management
- Implement proper UAC handling
- Service-based architecture for persistent operations
- Secure communication between components

This implementation guide provides the foundation for building a comprehensive VMware control and evasion system. The next step would be to create detailed code examples for each component.
