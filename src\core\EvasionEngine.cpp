#include "EvasionEngine.h"
#include <windows.h>
#include <winreg.h>
#include <tlhelp32.h>
#include <psapi.h>
#include <iostream>
#include <algorithm>

EvasionEngine::EvasionEngine() : isActive(false) {
    InitializeEvasionData();
}

EvasionEngine::~EvasionEngine() {
    if (isActive) {
        DisableAllEvasion();
    }
}

void EvasionEngine::InitializeEvasionData() {
    // VMware registry keys to hide/modify
    vmwareRegistryKeys = {
        "HKEY_LOCAL_MACHINE\\SOFTWARE\\VMware, Inc.\\VMware Tools",
        "HKEY_LOCAL_MACHINE\\SYSTEM\\ControlSet001\\Services\\vmdebug",
        "HKEY_LOCAL_MACHINE\\SYSTEM\\ControlSet001\\Services\\vmmouse",
        "HKEY_LOCAL_MACHINE\\SYSTEM\\ControlSet001\\Services\\VMTools",
        "HKEY_LOCAL_MACHINE\\SYSTEM\\ControlSet001\\Services\\VMMEMCTL",
        "HKEY_LOCAL_MACHINE\\SYSTEM\\ControlSet001\\Services\\vmware",
        "HKEY_LOCAL_MACHINE\\SYSTEM\\ControlSet001\\Services\\vmci",
        "HKEY_LOCAL_MACHINE\\SYSTEM\\ControlSet001\\Services\\vmx86",
        "HKEY_LOCAL_MACHINE\\SYSTEM\\ControlSet001\\Services\\vmkbd",
        "HKEY_LOCAL_MACHINE\\SYSTEM\\ControlSet001\\Services\\vmrawdsk",
        "HKEY_LOCAL_MACHINE\\SYSTEM\\ControlSet001\\Services\\vmusbmouse"
    };

    // VMware files and directories to hide
    vmwareFiles = {
        "C:\\Program Files\\VMware\\VMware Tools\\",
        "C:\\Program Files (x86)\\VMware\\VMware Tools\\",
        "C:\\Windows\\System32\\drivers\\vmmouse.sys",
        "C:\\Windows\\System32\\drivers\\vmhgfs.sys",
        "C:\\Windows\\System32\\drivers\\vmxnet.sys",
        "C:\\Windows\\System32\\drivers\\vmci.sys",
        "C:\\Windows\\System32\\drivers\\vmx86.sys",
        "C:\\Windows\\System32\\drivers\\vmrawdsk.sys",
        "C:\\Windows\\System32\\drivers\\vmusbmouse.sys"
    };

    // VMware processes to hide
    vmwareProcesses = {
        "vmtoolsd.exe",
        "vmwaretray.exe",
        "vmwareuser.exe",
        "VGAuthService.exe",
        "vmacthlp.exe",
        "vmware.exe",
        "vmplayer.exe"
    };

    // Hardware identifiers that indicate virtualization
    vmwareHardwareStrings = {
        "VMware",
        "VMware Virtual Platform",
        "VMware, Inc.",
        "VMware Virtual S",
        "VMware Virtual SCSI",
        "VMware Virtual IDE",
        "VMware Virtual Ethernet"
    };
}

bool EvasionEngine::EnableAllEvasion() {
    bool success = true;
    
    std::cout << "Enabling comprehensive VM detection evasion..." << std::endl;
    
    // Registry evasion
    if (!EnableRegistryEvasion()) {
        std::cerr << "Failed to enable registry evasion" << std::endl;
        success = false;
    }
    
    // File system evasion
    if (!EnableFileSystemEvasion()) {
        std::cerr << "Failed to enable file system evasion" << std::endl;
        success = false;
    }
    
    // Process evasion
    if (!EnableProcessEvasion()) {
        std::cerr << "Failed to enable process evasion" << std::endl;
        success = false;
    }
    
    // Hardware spoofing
    if (!EnableHardwareSpoofing()) {
        std::cerr << "Failed to enable hardware spoofing" << std::endl;
        success = false;
    }
    
    // Network adapter spoofing
    if (!EnableNetworkSpoofing()) {
        std::cerr << "Failed to enable network spoofing" << std::endl;
        success = false;
    }
    
    if (success) {
        isActive = true;
        std::cout << "VM detection evasion successfully enabled" << std::endl;
    }
    
    return success;
}

bool EvasionEngine::DisableAllEvasion() {
    bool success = true;
    
    std::cout << "Disabling VM detection evasion..." << std::endl;
    
    // Restore original registry values
    if (!RestoreRegistryValues()) {
        std::cerr << "Failed to restore registry values" << std::endl;
        success = false;
    }
    
    // Remove file system hooks
    if (!DisableFileSystemEvasion()) {
        std::cerr << "Failed to disable file system evasion" << std::endl;
        success = false;
    }
    
    // Remove process hooks
    if (!DisableProcessEvasion()) {
        std::cerr << "Failed to disable process evasion" << std::endl;
        success = false;
    }
    
    // Restore hardware information
    if (!RestoreHardwareInfo()) {
        std::cerr << "Failed to restore hardware information" << std::endl;
        success = false;
    }
    
    if (success) {
        isActive = false;
        std::cout << "VM detection evasion successfully disabled" << std::endl;
    }
    
    return success;
}

bool EvasionEngine::EnableRegistryEvasion() {
    std::cout << "Enabling registry evasion..." << std::endl;
    
    for (const auto& keyPath : vmwareRegistryKeys) {
        // Parse registry path
        HKEY rootKey;
        std::string subKey;
        if (!ParseRegistryPath(keyPath, rootKey, subKey)) {
            continue;
        }
        
        // Backup original value before modification
        std::string originalValue;
        if (ReadRegistryKey(rootKey, subKey, originalValue)) {
            originalRegistryValues[keyPath] = originalValue;
        }
        
        // Hide the registry key by renaming or deleting
        if (!HideRegistryKey(rootKey, subKey)) {
            std::cerr << "Failed to hide registry key: " << keyPath << std::endl;
        }
    }
    
    // Modify system information in registry
    ModifySystemInfoRegistry();
    
    return true;
}

bool EvasionEngine::EnableFileSystemEvasion() {
    std::cout << "Enabling file system evasion..." << std::endl;
    
    // This would typically involve installing a file system filter driver
    // For demonstration, we'll use API hooking approach
    
    // Hook file system APIs
    if (!HookFileSystemAPIs()) {
        std::cerr << "Failed to hook file system APIs" << std::endl;
        return false;
    }
    
    return true;
}

bool EvasionEngine::EnableProcessEvasion() {
    std::cout << "Enabling process evasion..." << std::endl;
    
    // Hook process enumeration APIs
    if (!HookProcessAPIs()) {
        std::cerr << "Failed to hook process APIs" << std::endl;
        return false;
    }
    
    return true;
}

bool EvasionEngine::EnableHardwareSpoofing() {
    std::cout << "Enabling hardware spoofing..." << std::endl;
    
    // Modify WMI queries
    if (!ModifyWMIQueries()) {
        std::cerr << "Failed to modify WMI queries" << std::endl;
        return false;
    }
    
    // Spoof CPUID instruction
    if (!SpoofCPUID()) {
        std::cerr << "Failed to spoof CPUID" << std::endl;
        return false;
    }
    
    return true;
}

bool EvasionEngine::EnableNetworkSpoofing() {
    std::cout << "Enabling network adapter spoofing..." << std::endl;
    
    // Change MAC addresses to avoid VMware OUIs
    std::vector<std::string> adapters = GetNetworkAdapters();
    
    for (const auto& adapter : adapters) {
        std::string currentMAC = GetAdapterMAC(adapter);
        if (IsVMwareMAC(currentMAC)) {
            std::string newMAC = GenerateRandomMAC();
            if (SetAdapterMAC(adapter, newMAC)) {
                originalMACAddresses[adapter] = currentMAC;
                std::cout << "Changed MAC address for " << adapter << " from " << currentMAC << " to " << newMAC << std::endl;
            }
        }
    }
    
    return true;
}

bool EvasionEngine::HideRegistryKey(HKEY rootKey, const std::string& subKey) {
    // Method 1: Rename the key to make it inaccessible
    std::string hiddenKeyName = subKey + "_hidden_" + std::to_string(GetTickCount());
    
    HKEY hKey;
    LONG result = RegOpenKeyExA(rootKey, subKey.c_str(), 0, KEY_ALL_ACCESS, &hKey);
    if (result == ERROR_SUCCESS) {
        RegCloseKey(hKey);
        
        // Rename the key
        result = RegRenameKey(rootKey, subKey.c_str(), hiddenKeyName.c_str());
        if (result == ERROR_SUCCESS) {
            renamedKeys[subKey] = hiddenKeyName;
            return true;
        }
    }
    
    return false;
}

bool EvasionEngine::ModifySystemInfoRegistry() {
    // Modify system manufacturer and model
    struct SystemInfo {
        std::string keyPath;
        std::string valueName;
        std::string newValue;
        std::string originalValue;
    };
    
    std::vector<SystemInfo> modifications = {
        {"HKEY_LOCAL_MACHINE\\HARDWARE\\DESCRIPTION\\System\\BIOS", "SystemManufacturer", "Dell Inc.", ""},
        {"HKEY_LOCAL_MACHINE\\HARDWARE\\DESCRIPTION\\System\\BIOS", "SystemProductName", "OptiPlex 7090", ""},
        {"HKEY_LOCAL_MACHINE\\HARDWARE\\DESCRIPTION\\System\\BIOS", "BIOSVendor", "Dell Inc.", ""},
        {"HKEY_LOCAL_MACHINE\\HARDWARE\\DESCRIPTION\\System\\BIOS", "BIOSVersion", "2.18.0", ""}
    };
    
    for (auto& mod : modifications) {
        HKEY rootKey;
        std::string subKey;
        if (ParseRegistryPath(mod.keyPath, rootKey, subKey)) {
            // Backup original value
            ReadRegistryValue(rootKey, subKey, mod.valueName, mod.originalValue);
            originalSystemInfo.push_back(mod);
            
            // Set new value
            WriteRegistryValue(rootKey, subKey, mod.valueName, mod.newValue);
        }
    }
    
    return true;
}

bool EvasionEngine::HookFileSystemAPIs() {
    // This would involve DLL injection and API hooking
    // For demonstration purposes, showing the concept
    
    // Hook CreateFileA/W, FindFirstFileA/W, etc.
    // When these APIs are called with VMware-related paths, return FILE_NOT_FOUND
    
    return InstallAPIHooks();
}

bool EvasionEngine::HookProcessAPIs() {
    // Hook CreateToolhelp32Snapshot, Process32First/Next
    // Filter out VMware processes from enumeration
    
    return InstallProcessHooks();
}

std::string EvasionEngine::GenerateRandomMAC() {
    // Generate a random MAC address avoiding VMware OUIs
    // VMware OUIs: 00:0C:29, 00:50:56, 00:1C:14
    
    std::string mac = "02:"; // Locally administered unicast
    
    for (int i = 0; i < 5; i++) {
        if (i > 0) mac += ":";
        int byte = rand() % 256;
        char hex[3];
        sprintf_s(hex, sizeof(hex), "%02X", byte);
        mac += hex;
    }
    
    return mac;
}

bool EvasionEngine::IsVMwareMAC(const std::string& mac) {
    std::string upperMAC = mac;
    std::transform(upperMAC.begin(), upperMAC.end(), upperMAC.begin(), ::toupper);
    
    return (upperMAC.substr(0, 8) == "00:0C:29" ||
            upperMAC.substr(0, 8) == "00:50:56" ||
            upperMAC.substr(0, 8) == "00:1C:14");
}

bool EvasionEngine::ParseRegistryPath(const std::string& fullPath, HKEY& rootKey, std::string& subKey) {
    if (fullPath.find("HKEY_LOCAL_MACHINE") == 0) {
        rootKey = HKEY_LOCAL_MACHINE;
        subKey = fullPath.substr(19); // Remove "HKEY_LOCAL_MACHINE\\"
        return true;
    } else if (fullPath.find("HKEY_CURRENT_USER") == 0) {
        rootKey = HKEY_CURRENT_USER;
        subKey = fullPath.substr(18); // Remove "HKEY_CURRENT_USER\\"
        return true;
    }
    
    return false;
}

bool EvasionEngine::ReadRegistryValue(HKEY rootKey, const std::string& subKey, const std::string& valueName, std::string& value) {
    HKEY hKey;
    LONG result = RegOpenKeyExA(rootKey, subKey.c_str(), 0, KEY_READ, &hKey);
    if (result != ERROR_SUCCESS) {
        return false;
    }
    
    DWORD dataSize = 0;
    result = RegQueryValueExA(hKey, valueName.c_str(), NULL, NULL, NULL, &dataSize);
    if (result == ERROR_SUCCESS && dataSize > 0) {
        std::vector<char> buffer(dataSize);
        result = RegQueryValueExA(hKey, valueName.c_str(), NULL, NULL, (LPBYTE)buffer.data(), &dataSize);
        if (result == ERROR_SUCCESS) {
            value = std::string(buffer.data(), dataSize - 1); // Remove null terminator
        }
    }
    
    RegCloseKey(hKey);
    return (result == ERROR_SUCCESS);
}

bool EvasionEngine::WriteRegistryValue(HKEY rootKey, const std::string& subKey, const std::string& valueName, const std::string& value) {
    HKEY hKey;
    LONG result = RegOpenKeyExA(rootKey, subKey.c_str(), 0, KEY_WRITE, &hKey);
    if (result != ERROR_SUCCESS) {
        return false;
    }
    
    result = RegSetValueExA(hKey, valueName.c_str(), 0, REG_SZ, (const BYTE*)value.c_str(), value.length() + 1);
    RegCloseKey(hKey);
    
    return (result == ERROR_SUCCESS);
}
