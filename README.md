# VMware Controller & Detection Evasion Tool

A comprehensive application for controlling VMware virtual machines and implementing detection evasion techniques to make VMs appear as physical machines.

## ⚠️ Important Legal Notice

This software is intended for legitimate purposes only, including:
- Security research and testing
- Malware analysis in controlled environments  
- Educational purposes
- Authorized penetration testing

Users are responsible for ensuring compliance with all applicable laws and regulations. The developers assume no responsibility for misuse of this software.

## Features

### VMware Control Capabilities
- **VM Lifecycle Management**: Start, stop, pause, resume, and reset virtual machines
- **VM Discovery**: Automatically detect and catalog VMware VMs on the system
- **Configuration Management**: Modify VM settings and apply anti-detection configurations
- **Snapshot Management**: Create, restore, and delete VM snapshots
- **Resource Monitoring**: Monitor CPU, memory, and disk usage
- **Guest Operations**: Execute commands and transfer files (requires VMware Tools)

### Detection Evasion Features
- **Registry Evasion**: Hide or modify VMware-related registry entries
- **File System Masking**: Hide VMware files and directories from detection
- **Process Hiding**: Conceal VMware processes from enumeration
- **Hardware Spoofing**: Modify system information and hardware identifiers
- **Network Adapter Management**: Change MAC addresses to avoid VMware OUIs
- **CPUID Spoofing**: Modify CPU identification to hide hypervisor presence
- **WMI Query Modification**: Alter Windows Management Instrumentation responses

## System Requirements

- **Operating System**: Windows 10/11 (64-bit recommended)
- **VMware Software**: VMware Workstation Pro 15+ or VMware Player
- **Privileges**: Administrator rights required for evasion features
- **Development**: Visual Studio 2019+ with C++17 support
- **Dependencies**: VMware VIX API, Windows SDK

## Installation & Setup

### 1. Prerequisites

#### Install VMware VIX API
1. Download VMware VIX API from VMware website
2. Install to default location: `C:\Program Files (x86)\VMware\VMware VIX`
3. Ensure the installation includes headers and libraries

#### Install Development Tools
```bash
# Install Visual Studio 2022 with C++ development tools
# Install CMake 3.16 or later
# Install Git for version control
```

### 2. Build Instructions

#### Using CMake (Recommended)
```bash
# Clone the repository
git clone <repository-url>
cd VMwareController

# Create build directory
mkdir build
cd build

# Configure the project
cmake .. -DCMAKE_BUILD_TYPE=Release

# Build the project
cmake --build . --config Release

# The executable will be in build/bin/VMwareController.exe
```

#### Using Visual Studio
1. Open the project folder in Visual Studio
2. Configure CMake settings if needed
3. Build the solution (Ctrl+Shift+B)

### 3. Configuration

#### VMware VIX API Setup
If VIX API is installed in a non-standard location:
```bash
cmake .. -DVMWARE_VIX_ROOT="C:\Path\To\VMware\VIX"
```

#### Build Options
```bash
# Build with tests
cmake .. -DBUILD_TESTS=ON

# Build with examples
cmake .. -DBUILD_EXAMPLES=ON

# Build kernel drivers (requires WDK)
cmake .. -DBUILD_DRIVERS=ON
```

## Usage

### Basic Usage

#### Starting the Application
```bash
# Run as administrator for full functionality
VMwareController.exe
```

#### Main Menu Options
1. **List Virtual Machines** - Discover and display all VMs
2. **Start Virtual Machine** - Power on a selected VM
3. **Stop Virtual Machine** - Power off a selected VM
4. **VM Status** - Check current status of all VMs
5. **Apply Anti-Detection Config** - Modify VM settings to avoid detection
6. **Enable Detection Evasion** - Activate system-wide evasion techniques
7. **Disable Detection Evasion** - Restore original system settings
8. **Test VM Detection** - Verify effectiveness of evasion techniques
9. **Network Adapter Management** - Modify MAC addresses

### Advanced Configuration

#### Custom Evasion Profiles
Create custom evasion profiles by modifying the configuration:

```cpp
EvasionEngine::EvasionProfile customProfile;
customProfile.registryEvasion = true;
customProfile.fileSystemEvasion = true;
customProfile.hardwareSpoofing = true;
customProfile.systemManufacturer = "Dell Inc.";
customProfile.systemModel = "OptiPlex 7090";
customProfile.randomizeMAC = true;

evasionEngine->ApplyProfile(customProfile);
```

#### VM Configuration Changes
Apply anti-detection settings to VM configuration:

```cpp
ConfigChanges changes;
changes.ApplyAntiDetectionSettings();
vmEngine->ModifyVMConfig("path/to/vm.vmx", changes);
```

## API Reference

### VMwareEngine Class

#### Core Methods
```cpp
// Initialize the VMware engine
bool Initialize();

// Discover all VMs on the system
std::vector<VMInfo> DiscoverVMs();

// VM lifecycle operations
bool StartVM(const std::string& vmxPath);
bool StopVM(const std::string& vmxPath);
bool PauseVM(const std::string& vmxPath);
bool ResumeVM(const std::string& vmxPath);

// Configuration management
bool ModifyVMConfig(const std::string& vmxPath, const ConfigChanges& changes);
bool ApplyAntiDetectionConfig(const std::string& vmxPath);

// Snapshot operations
bool CreateSnapshot(const std::string& vmxPath, const std::string& name);
bool RestoreSnapshot(const std::string& vmxPath, const std::string& name);
```

### EvasionEngine Class

#### Evasion Control
```cpp
// Enable/disable all evasion techniques
bool EnableAllEvasion();
bool DisableAllEvasion();

// Individual evasion components
bool EnableRegistryEvasion();
bool EnableFileSystemEvasion();
bool EnableProcessEvasion();
bool EnableHardwareSpoofing();
bool EnableNetworkSpoofing();

// Network management
std::vector<std::string> GetNetworkAdapters();
bool SetAdapterMAC(const std::string& adapter, const std::string& mac);

// Detection testing
bool TestDetectionEvasion();
bool IsVMDetected();
```

## Security Considerations

### Code Signing
- All kernel drivers must be properly signed for Windows 10/11
- Consider using cross-signing certificates for compatibility
- Implement proper certificate management and validation

### Anti-Analysis Protection
- The application includes basic anti-debugging techniques
- Code obfuscation is recommended for sensitive components
- Runtime packing/encryption can be applied to critical modules

### Privilege Management
- Administrator privileges are required for most evasion techniques
- The application implements proper UAC handling
- Service-based architecture ensures persistent operation

## Troubleshooting

### Common Issues

#### "Failed to initialize VMware engine"
- Ensure VMware Workstation/Player is installed
- Verify VIX API installation
- Check that VMware services are running

#### "Access denied" errors
- Run the application as administrator
- Verify user has necessary privileges
- Check Windows UAC settings

#### Evasion techniques not working
- Ensure administrator privileges
- Verify Windows version compatibility
- Check antivirus software interference

### Debug Mode
Enable verbose logging for troubleshooting:
```cpp
evasionEngine->SetVerbose(true);
```

## Development

### Project Structure
```
VMwareController/
├── src/
│   ├── core/           # Core engine implementations
│   ├── utils/          # Utility functions
│   ├── drivers/        # Kernel drivers (optional)
│   └── ui/             # User interface components
├── include/            # Header files
├── config/             # Configuration files
├── docs/               # Documentation
└── tests/              # Unit tests
```

### Contributing
1. Fork the repository
2. Create a feature branch
3. Implement changes with proper testing
4. Submit a pull request with detailed description

### Testing
```bash
# Build with tests enabled
cmake .. -DBUILD_TESTS=ON
cmake --build . --config Release

# Run tests
ctest --output-on-failure
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Disclaimer

This software is provided for educational and research purposes only. The authors and contributors are not responsible for any misuse or damage caused by this software. Users must ensure compliance with all applicable laws and regulations in their jurisdiction.

## Support

For technical support and questions:
- Create an issue on the project repository
- Review the documentation in the `docs/` directory
- Check the troubleshooting section above

## Acknowledgments

- VMware for providing the VIX API
- The security research community for detection techniques
- Contributors and testers who helped improve this project
