#pragma once

#include <string>
#include <vector>
#include <map>
#include <windows.h>

class EvasionEngine {
public:
    struct EvasionProfile {
        bool registryEvasion = true;
        bool fileSystemEvasion = true;
        bool processEvasion = true;
        bool hardwareSpoofing = true;
        bool networkSpoofing = true;
        bool cpuidSpoofing = true;
        bool wmiSpoofing = true;
        
        // Custom hardware information
        std::string systemManufacturer = "Dell Inc.";
        std::string systemModel = "OptiPlex 7090";
        std::string biosVendor = "Dell Inc.";
        std::string biosVersion = "2.18.0";
        
        // Network settings
        bool randomizeMAC = true;
        std::string customMAC = "";
    };

    struct SystemInfo {
        std::string keyPath;
        std::string valueName;
        std::string newValue;
        std::string originalValue;
    };

private:
    bool isActive;
    EvasionProfile currentProfile;
    
    // Data storage for restoration
    std::map<std::string, std::string> originalRegistryValues;
    std::map<std::string, std::string> originalMACAddresses;
    std::map<std::string, std::string> renamedKeys;
    std::vector<SystemInfo> originalSystemInfo;
    
    // VMware detection artifacts
    std::vector<std::string> vmwareRegistryKeys;
    std::vector<std::string> vmwareFiles;
    std::vector<std::string> vmwareProcesses;
    std::vector<std::string> vmwareHardwareStrings;
    
    // API hook handles
    std::map<std::string, FARPROC> originalAPIs;
    std::vector<HMODULE> hookedModules;

public:
    EvasionEngine();
    ~EvasionEngine();
    
    // Main control methods
    bool EnableAllEvasion();
    bool DisableAllEvasion();
    bool ApplyProfile(const EvasionProfile& profile);
    bool IsEvasionActive() const { return isActive; }
    
    // Individual evasion components
    bool EnableRegistryEvasion();
    bool EnableFileSystemEvasion();
    bool EnableProcessEvasion();
    bool EnableHardwareSpoofing();
    bool EnableNetworkSpoofing();
    bool EnableCPUIDSpoofing();
    bool EnableWMISpoofing();
    
    // Disable individual components
    bool DisableRegistryEvasion();
    bool DisableFileSystemEvasion();
    bool DisableProcessEvasion();
    bool DisableHardwareSpoofing();
    bool DisableNetworkSpoofing();
    bool DisableCPUIDSpoofing();
    bool DisableWMISpoofing();
    
    // Registry operations
    bool HideRegistryKey(HKEY rootKey, const std::string& subKey);
    bool RestoreRegistryKey(HKEY rootKey, const std::string& subKey);
    bool ModifyRegistryValue(HKEY rootKey, const std::string& subKey, 
                           const std::string& valueName, const std::string& newValue);
    bool RestoreRegistryValues();
    
    // File system operations
    bool HideFile(const std::string& filePath);
    bool HideDirectory(const std::string& dirPath);
    bool InstallFileSystemFilter();
    bool UninstallFileSystemFilter();
    
    // Process operations
    bool HideProcess(const std::string& processName);
    bool HideProcessFromPID(DWORD processId);
    std::vector<std::string> GetHiddenProcesses() const;
    
    // Hardware spoofing
    bool SpoofSystemManufacturer(const std::string& manufacturer);
    bool SpoofSystemModel(const std::string& model);
    bool SpoofBIOSInfo(const std::string& vendor, const std::string& version);
    bool SpoofCPUID();
    bool RestoreHardwareInfo();
    
    // Network operations
    std::vector<std::string> GetNetworkAdapters();
    std::string GetAdapterMAC(const std::string& adapterName);
    bool SetAdapterMAC(const std::string& adapterName, const std::string& macAddress);
    bool RestoreNetworkSettings();
    
    // Detection testing
    bool TestDetectionEvasion();
    std::vector<std::string> GetDetectionResults();
    bool IsVMDetected();
    
    // Configuration
    void SetProfile(const EvasionProfile& profile) { currentProfile = profile; }
    EvasionProfile GetProfile() const { return currentProfile; }
    void SaveProfile(const std::string& filePath);
    bool LoadProfile(const std::string& filePath);
    
    // Utility methods
    std::string GetLastError() const { return lastError; }
    void SetVerbose(bool verbose) { verboseOutput = verbose; }
    
private:
    std::string lastError;
    bool verboseOutput = false;
    
    // Initialization
    void InitializeEvasionData();
    void LoadDefaultProfile();
    
    // Registry helpers
    bool ParseRegistryPath(const std::string& fullPath, HKEY& rootKey, std::string& subKey);
    bool ReadRegistryKey(HKEY rootKey, const std::string& subKey, std::string& value);
    bool ReadRegistryValue(HKEY rootKey, const std::string& subKey, 
                          const std::string& valueName, std::string& value);
    bool WriteRegistryValue(HKEY rootKey, const std::string& subKey, 
                           const std::string& valueName, const std::string& value);
    bool DeleteRegistryKey(HKEY rootKey, const std::string& subKey);
    LONG RegRenameKey(HKEY hKey, LPCSTR lpSubKeyName, LPCSTR lpNewKeyName);
    
    // System information modification
    bool ModifySystemInfoRegistry();
    bool ModifyWMIQueries();
    bool HookWMIAPIs();
    
    // API hooking infrastructure
    bool InstallAPIHooks();
    bool RemoveAPIHooks();
    bool HookFunction(const std::string& moduleName, const std::string& functionName, FARPROC hookFunction);
    bool UnhookFunction(const std::string& moduleName, const std::string& functionName);
    
    // File system hooks
    bool HookFileSystemAPIs();
    bool UnhookFileSystemAPIs();
    static HANDLE WINAPI HookedCreateFileA(LPCSTR lpFileName, DWORD dwDesiredAccess, 
                                          DWORD dwShareMode, LPSECURITY_ATTRIBUTES lpSecurityAttributes,
                                          DWORD dwCreationDisposition, DWORD dwFlagsAndAttributes, HANDLE hTemplateFile);
    static HANDLE WINAPI HookedCreateFileW(LPCWSTR lpFileName, DWORD dwDesiredAccess, 
                                          DWORD dwShareMode, LPSECURITY_ATTRIBUTES lpSecurityAttributes,
                                          DWORD dwCreationDisposition, DWORD dwFlagsAndAttributes, HANDLE hTemplateFile);
    static HANDLE WINAPI HookedFindFirstFileA(LPCSTR lpFileName, LPWIN32_FIND_DATAA lpFindFileData);
    static HANDLE WINAPI HookedFindFirstFileW(LPCWSTR lpFileName, LPWIN32_FIND_DATAW lpFindFileData);
    
    // Process hooks
    bool HookProcessAPIs();
    bool UnhookProcessAPIs();
    static HANDLE WINAPI HookedCreateToolhelp32Snapshot(DWORD dwFlags, DWORD th32ProcessID);
    static BOOL WINAPI HookedProcess32First(HANDLE hSnapshot, LPPROCESSENTRY32 lppe);
    static BOOL WINAPI HookedProcess32Next(HANDLE hSnapshot, LPPROCESSENTRY32 lppe);
    
    // CPUID hooks
    bool InstallCPUIDHook();
    bool RemoveCPUIDHook();
    static void HookedCPUID(int cpuInfo[4], int function_id);
    
    // Network utilities
    std::string GenerateRandomMAC();
    bool IsVMwareMAC(const std::string& mac);
    bool IsValidMAC(const std::string& mac);
    
    // Detection utilities
    bool IsVMwareFile(const std::string& filePath);
    bool IsVMwareProcess(const std::string& processName);
    bool IsVMwareRegistryKey(const std::string& keyPath);
    bool ContainsVMwareString(const std::string& text);
    
    // Logging and error handling
    void LogMessage(const std::string& message);
    void LogError(const std::string& error);
    void SetLastError(const std::string& error) { lastError = error; }
    
    // Static instance for hook callbacks
    static EvasionEngine* instance;
    
    // Thread safety
    CRITICAL_SECTION criticalSection;
    void InitializeCriticalSection() { ::InitializeCriticalSection(&criticalSection); }
    void DeleteCriticalSection() { ::DeleteCriticalSection(&criticalSection); }
    void EnterCriticalSection() { ::EnterCriticalSection(&criticalSection); }
    void LeaveCriticalSection() { ::LeaveCriticalSection(&criticalSection); }
};
