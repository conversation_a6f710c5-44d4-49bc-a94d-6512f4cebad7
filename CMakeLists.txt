cmake_minimum_required(VERSION 3.16)
project(VMwareController VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set build type if not specified
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Compiler-specific options
if(MSVC)
    add_compile_options(/W4 /WX-)
    add_compile_definitions(_CRT_SECURE_NO_WARNINGS)
    add_compile_definitions(WIN32_LEAN_AND_MEAN)
    add_compile_definitions(NOMINMAX)
else()
    add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# Include directories
include_directories(${CMAKE_SOURCE_DIR}/src)
include_directories(${CMAKE_SOURCE_DIR}/include)

# Find required packages
find_package(Threads REQUIRED)

# VMware VIX API (you'll need to download and install VMware VIX API)
# Set VMWARE_VIX_ROOT to the installation directory
set(VMWARE_VIX_ROOT "C:/Program Files (x86)/VMware/VMware VIX" CACHE PATH "VMware VIX installation directory")

if(EXISTS "${VMWARE_VIX_ROOT}")
    include_directories("${VMWARE_VIX_ROOT}/Include")
    if(CMAKE_SIZEOF_VOID_P EQUAL 8)
        link_directories("${VMWARE_VIX_ROOT}/Lib/x64")
    else()
        link_directories("${VMWARE_VIX_ROOT}/Lib/x86")
    endif()
    set(VIX_FOUND TRUE)
else()
    message(WARNING "VMware VIX API not found. Please install VMware VIX API and set VMWARE_VIX_ROOT")
    set(VIX_FOUND FALSE)
endif()

# Source files
set(CORE_SOURCES
    src/core/VMwareEngine.cpp
    src/core/EvasionEngine.cpp
)

set(UTILS_SOURCES
    src/utils/SystemUtils.cpp
    src/utils/VMwareUtils.cpp
)

set(MAIN_SOURCES
    src/main.cpp
)

# Create core library
add_library(VMwareControllerCore STATIC ${CORE_SOURCES})

# Create utils library
add_library(VMwareControllerUtils STATIC ${UTILS_SOURCES})

# Main executable
add_executable(VMwareController ${MAIN_SOURCES})

# Link libraries
target_link_libraries(VMwareController 
    VMwareControllerCore
    VMwareControllerUtils
    Threads::Threads
)

# Windows-specific libraries
if(WIN32)
    target_link_libraries(VMwareController
        advapi32    # Registry operations
        kernel32    # System APIs
        user32      # User interface
        shell32     # Shell operations
        ole32       # COM operations
        oleaut32    # OLE automation
        wbemuuid    # WMI operations
        psapi       # Process APIs
        iphlpapi    # Network APIs
        ws2_32      # Winsock
        netapi32    # Network management
    )
    
    # VMware VIX library
    if(VIX_FOUND)
        target_link_libraries(VMwareController vix)
        target_compile_definitions(VMwareController PRIVATE VIX_AVAILABLE)
    endif()
endif()

# Compiler definitions
target_compile_definitions(VMwareController PRIVATE
    UNICODE
    _UNICODE
    WIN32_LEAN_AND_MEAN
    NOMINMAX
)

# Set output directories
set_target_properties(VMwareController PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
    LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib
    ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib
)

# Installation
install(TARGETS VMwareController
    RUNTIME DESTINATION bin
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)

# Copy configuration files
install(DIRECTORY config/
    DESTINATION config
    FILES_MATCHING PATTERN "*.json"
)

# Copy documentation
install(FILES 
    VMware_Control_Analysis.md
    Implementation_Guide.md
    README.md
    DESTINATION docs
)

# Development tools and utilities
option(BUILD_TESTS "Build test suite" OFF)
option(BUILD_EXAMPLES "Build example applications" OFF)
option(BUILD_DRIVERS "Build kernel drivers" OFF)

if(BUILD_TESTS)
    enable_testing()
    add_subdirectory(tests)
endif()

if(BUILD_EXAMPLES)
    add_subdirectory(examples)
endif()

if(BUILD_DRIVERS)
    # Note: Building Windows kernel drivers requires WDK
    # This would need additional configuration
    message(STATUS "Driver building requested but not implemented in this CMake file")
endif()

# Package configuration
set(CPACK_PACKAGE_NAME "VMwareController")
set(CPACK_PACKAGE_VERSION ${PROJECT_VERSION})
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY "VMware Control and Detection Evasion Tool")
set(CPACK_PACKAGE_VENDOR "Your Company")
set(CPACK_PACKAGE_CONTACT "<EMAIL>")

if(WIN32)
    set(CPACK_GENERATOR "NSIS;ZIP")
    set(CPACK_NSIS_DISPLAY_NAME "VMware Controller")
    set(CPACK_NSIS_PACKAGE_NAME "VMware Controller")
    set(CPACK_NSIS_CONTACT "<EMAIL>")
    set(CPACK_NSIS_HELP_LINK "https://your-website.com")
    set(CPACK_NSIS_URL_INFO_ABOUT "https://your-website.com")
    set(CPACK_NSIS_MODIFY_PATH ON)
endif()

include(CPack)

# Print configuration summary
message(STATUS "")
message(STATUS "VMware Controller Configuration Summary:")
message(STATUS "  Version: ${PROJECT_VERSION}")
message(STATUS "  Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  VMware VIX API: ${VIX_FOUND}")
if(VIX_FOUND)
    message(STATUS "  VIX Root: ${VMWARE_VIX_ROOT}")
endif()
message(STATUS "  Build tests: ${BUILD_TESTS}")
message(STATUS "  Build examples: ${BUILD_EXAMPLES}")
message(STATUS "  Build drivers: ${BUILD_DRIVERS}")
message(STATUS "")
