#include <iostream>
#include <string>
#include <vector>
#include <memory>
#include <windows.h>
#include "core/VMwareEngine.h"
#include "core/EvasionEngine.h"

class VMwareController {
private:
    std::unique_ptr<VMwareEngine> vmEngine;
    std::unique_ptr<EvasionEngine> evasionEngine;
    bool isInitialized;

public:
    VMwareController() : isInitialized(false) {
        vmEngine = std::make_unique<VMwareEngine>();
        evasionEngine = std::make_unique<EvasionEngine>();
    }

    bool Initialize() {
        std::cout << "Initializing VMware Controller..." << std::endl;
        
        if (!vmEngine->Initialize()) {
            std::cerr << "Failed to initialize VMware engine" << std::endl;
            return false;
        }
        
        isInitialized = true;
        std::cout << "VMware Controller initialized successfully" << std::endl;
        return true;
    }

    void ShowMainMenu() {
        while (true) {
            std::cout << "\n=== VMware Controller & Evasion Tool ===" << std::endl;
            std::cout << "1. List Virtual Machines" << std::endl;
            std::cout << "2. Start Virtual Machine" << std::endl;
            std::cout << "3. Stop Virtual Machine" << std::endl;
            std::cout << "4. VM Status" << std::endl;
            std::cout << "5. Apply Anti-Detection Config" << std::endl;
            std::cout << "6. Enable Detection Evasion" << std::endl;
            std::cout << "7. Disable Detection Evasion" << std::endl;
            std::cout << "8. Test VM Detection" << std::endl;
            std::cout << "9. Network Adapter Management" << std::endl;
            std::cout << "0. Exit" << std::endl;
            std::cout << "Choose option: ";

            int choice;
            std::cin >> choice;

            switch (choice) {
                case 1: ListVirtualMachines(); break;
                case 2: StartVirtualMachine(); break;
                case 3: StopVirtualMachine(); break;
                case 4: ShowVMStatus(); break;
                case 5: ApplyAntiDetectionConfig(); break;
                case 6: EnableDetectionEvasion(); break;
                case 7: DisableDetectionEvasion(); break;
                case 8: TestVMDetection(); break;
                case 9: ManageNetworkAdapters(); break;
                case 0: return;
                default: std::cout << "Invalid option" << std::endl; break;
            }
        }
    }

    void ListVirtualMachines() {
        std::cout << "\nScanning for virtual machines..." << std::endl;
        
        auto vmList = vmEngine->DiscoverVMs();
        
        if (vmList.empty()) {
            std::cout << "No virtual machines found." << std::endl;
            return;
        }

        std::cout << "\nFound " << vmList.size() << " virtual machine(s):" << std::endl;
        std::cout << "----------------------------------------" << std::endl;
        
        for (size_t i = 0; i < vmList.size(); i++) {
            const auto& vm = vmList[i];
            std::cout << "[" << i + 1 << "] " << vm.name << std::endl;
            std::cout << "    Display Name: " << vm.displayName << std::endl;
            std::cout << "    Path: " << vm.vmxPath << std::endl;
            std::cout << "    Status: " << StatusToString(vm.status) << std::endl;
            std::cout << std::endl;
        }
    }

    void StartVirtualMachine() {
        auto vmList = vmEngine->DiscoverVMs();
        if (vmList.empty()) {
            std::cout << "No virtual machines found." << std::endl;
            return;
        }

        std::cout << "\nSelect VM to start:" << std::endl;
        for (size_t i = 0; i < vmList.size(); i++) {
            std::cout << "[" << i + 1 << "] " << vmList[i].name << std::endl;
        }

        int choice;
        std::cout << "Enter choice: ";
        std::cin >> choice;

        if (choice < 1 || choice > static_cast<int>(vmList.size())) {
            std::cout << "Invalid selection." << std::endl;
            return;
        }

        const auto& selectedVM = vmList[choice - 1];
        std::cout << "Starting VM: " << selectedVM.name << std::endl;

        if (vmEngine->StartVM(selectedVM.vmxPath)) {
            std::cout << "VM started successfully!" << std::endl;
        } else {
            std::cout << "Failed to start VM: " << vmEngine->GetLastError() << std::endl;
        }
    }

    void StopVirtualMachine() {
        auto vmList = vmEngine->DiscoverVMs();
        if (vmList.empty()) {
            std::cout << "No virtual machines found." << std::endl;
            return;
        }

        std::cout << "\nSelect VM to stop:" << std::endl;
        for (size_t i = 0; i < vmList.size(); i++) {
            std::cout << "[" << i + 1 << "] " << vmList[i].name 
                      << " (" << StatusToString(vmList[i].status) << ")" << std::endl;
        }

        int choice;
        std::cout << "Enter choice: ";
        std::cin >> choice;

        if (choice < 1 || choice > static_cast<int>(vmList.size())) {
            std::cout << "Invalid selection." << std::endl;
            return;
        }

        const auto& selectedVM = vmList[choice - 1];
        std::cout << "Stopping VM: " << selectedVM.name << std::endl;

        if (vmEngine->StopVM(selectedVM.vmxPath)) {
            std::cout << "VM stopped successfully!" << std::endl;
        } else {
            std::cout << "Failed to stop VM: " << vmEngine->GetLastError() << std::endl;
        }
    }

    void ShowVMStatus() {
        auto vmList = vmEngine->DiscoverVMs();
        if (vmList.empty()) {
            std::cout << "No virtual machines found." << std::endl;
            return;
        }

        std::cout << "\nVirtual Machine Status:" << std::endl;
        std::cout << "----------------------------------------" << std::endl;
        
        for (const auto& vm : vmList) {
            VMStatus status = vmEngine->GetVMStatus(vm.vmxPath);
            std::cout << vm.name << ": " << StatusToString(status) << std::endl;
        }
    }

    void ApplyAntiDetectionConfig() {
        auto vmList = vmEngine->DiscoverVMs();
        if (vmList.empty()) {
            std::cout << "No virtual machines found." << std::endl;
            return;
        }

        std::cout << "\nSelect VM to apply anti-detection config:" << std::endl;
        for (size_t i = 0; i < vmList.size(); i++) {
            std::cout << "[" << i + 1 << "] " << vmList[i].name << std::endl;
        }

        int choice;
        std::cout << "Enter choice: ";
        std::cin >> choice;

        if (choice < 1 || choice > static_cast<int>(vmList.size())) {
            std::cout << "Invalid selection." << std::endl;
            return;
        }

        const auto& selectedVM = vmList[choice - 1];
        std::cout << "Applying anti-detection configuration to: " << selectedVM.name << std::endl;

        ConfigChanges changes;
        changes.ApplyAntiDetectionSettings();

        if (vmEngine->ModifyVMConfig(selectedVM.vmxPath, changes)) {
            std::cout << "Anti-detection configuration applied successfully!" << std::endl;
            std::cout << "Note: You may need to restart the VM for changes to take effect." << std::endl;
        } else {
            std::cout << "Failed to apply configuration: " << vmEngine->GetLastError() << std::endl;
        }
    }

    void EnableDetectionEvasion() {
        std::cout << "\nEnabling VM detection evasion..." << std::endl;
        std::cout << "WARNING: This will modify system settings and may require administrator privileges." << std::endl;
        std::cout << "Continue? (y/n): ";
        
        char confirm;
        std::cin >> confirm;
        
        if (confirm != 'y' && confirm != 'Y') {
            std::cout << "Operation cancelled." << std::endl;
            return;
        }

        if (evasionEngine->EnableAllEvasion()) {
            std::cout << "Detection evasion enabled successfully!" << std::endl;
            std::cout << "The system is now configured to hide VM artifacts." << std::endl;
        } else {
            std::cout << "Failed to enable detection evasion: " << evasionEngine->GetLastError() << std::endl;
        }
    }

    void DisableDetectionEvasion() {
        std::cout << "\nDisabling VM detection evasion..." << std::endl;
        
        if (evasionEngine->DisableAllEvasion()) {
            std::cout << "Detection evasion disabled successfully!" << std::endl;
            std::cout << "System settings have been restored." << std::endl;
        } else {
            std::cout << "Failed to disable detection evasion: " << evasionEngine->GetLastError() << std::endl;
        }
    }

    void TestVMDetection() {
        std::cout << "\nTesting VM detection..." << std::endl;
        
        if (evasionEngine->TestDetectionEvasion()) {
            std::cout << "Detection test completed." << std::endl;
            
            auto results = evasionEngine->GetDetectionResults();
            for (const auto& result : results) {
                std::cout << "- " << result << std::endl;
            }
            
            if (evasionEngine->IsVMDetected()) {
                std::cout << "\nResult: VM DETECTED - Evasion may need improvement" << std::endl;
            } else {
                std::cout << "\nResult: VM NOT DETECTED - Evasion appears effective" << std::endl;
            }
        } else {
            std::cout << "Failed to run detection test." << std::endl;
        }
    }

    void ManageNetworkAdapters() {
        std::cout << "\nNetwork Adapter Management" << std::endl;
        std::cout << "1. List Network Adapters" << std::endl;
        std::cout << "2. Change MAC Address" << std::endl;
        std::cout << "3. Restore Original MAC Addresses" << std::endl;
        std::cout << "Enter choice: ";

        int choice;
        std::cin >> choice;

        switch (choice) {
            case 1: {
                auto adapters = evasionEngine->GetNetworkAdapters();
                std::cout << "\nNetwork Adapters:" << std::endl;
                for (const auto& adapter : adapters) {
                    std::string mac = evasionEngine->GetAdapterMAC(adapter);
                    std::cout << adapter << ": " << mac << std::endl;
                }
                break;
            }
            case 2: {
                auto adapters = evasionEngine->GetNetworkAdapters();
                if (adapters.empty()) {
                    std::cout << "No network adapters found." << std::endl;
                    break;
                }

                std::cout << "Select adapter:" << std::endl;
                for (size_t i = 0; i < adapters.size(); i++) {
                    std::cout << "[" << i + 1 << "] " << adapters[i] << std::endl;
                }

                int adapterChoice;
                std::cin >> adapterChoice;

                if (adapterChoice < 1 || adapterChoice > static_cast<int>(adapters.size())) {
                    std::cout << "Invalid selection." << std::endl;
                    break;
                }

                std::string newMAC;
                std::cout << "Enter new MAC address (format: XX:XX:XX:XX:XX:XX) or 'random': ";
                std::cin >> newMAC;

                if (newMAC == "random") {
                    // Generate random MAC
                    newMAC = "02:"; // Locally administered
                    for (int i = 0; i < 5; i++) {
                        if (i > 0) newMAC += ":";
                        char hex[3];
                        sprintf_s(hex, "%02X", rand() % 256);
                        newMAC += hex;
                    }
                }

                if (evasionEngine->SetAdapterMAC(adapters[adapterChoice - 1], newMAC)) {
                    std::cout << "MAC address changed successfully!" << std::endl;
                } else {
                    std::cout << "Failed to change MAC address." << std::endl;
                }
                break;
            }
            case 3: {
                if (evasionEngine->RestoreNetworkSettings()) {
                    std::cout << "Network settings restored successfully!" << std::endl;
                } else {
                    std::cout << "Failed to restore network settings." << std::endl;
                }
                break;
            }
        }
    }

private:
    std::string StatusToString(VMStatus status) {
        switch (status) {
            case VMStatus::Running: return "Running";
            case VMStatus::Stopped: return "Stopped";
            case VMStatus::Suspended: return "Suspended";
            case VMStatus::Paused: return "Paused";
            default: return "Unknown";
        }
    }
};

int main() {
    std::cout << "VMware Controller & Detection Evasion Tool" << std::endl;
    std::cout << "===========================================" << std::endl;
    
    // Check for administrator privileges
    BOOL isAdmin = FALSE;
    PSID adminGroup = NULL;
    SID_IDENTIFIER_AUTHORITY ntAuthority = SECURITY_NT_AUTHORITY;
    
    if (AllocateAndInitializeSid(&ntAuthority, 2, SECURITY_BUILTIN_DOMAIN_RID,
                                DOMAIN_ALIAS_RID_ADMINS, 0, 0, 0, 0, 0, 0, &adminGroup)) {
        CheckTokenMembership(NULL, adminGroup, &isAdmin);
        FreeSid(adminGroup);
    }
    
    if (!isAdmin) {
        std::cout << "WARNING: Running without administrator privileges." << std::endl;
        std::cout << "Some features may not work properly." << std::endl;
        std::cout << std::endl;
    }

    VMwareController controller;
    
    if (!controller.Initialize()) {
        std::cerr << "Failed to initialize VMware Controller" << std::endl;
        std::cout << "Press Enter to exit...";
        std::cin.get();
        return 1;
    }

    controller.ShowMainMenu();
    
    std::cout << "Thank you for using VMware Controller!" << std::endl;
    return 0;
}
