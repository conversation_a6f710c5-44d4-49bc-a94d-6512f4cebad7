#include "VMwareEngine.h"
#include <iostream>
#include <fstream>
#include <sstream>
#include <filesystem>
#include <windows.h>
#include <tlhelp32.h>

VMwareEngine::VMwareEngine() : hostHandle(VIX_INVALID_HANDLE) {
    Initialize();
}

VMwareEngine::~VMwareEngine() {
    Cleanup();
}

bool VMwareEngine::Initialize() {
    VixError err = VixHost_Connect(VIX_API_VERSION,
                                   VIX_SERVICEPROVIDER_VMWARE_WORKSTATION,
                                   NULL, // hostName
                                   0,    // hostPort
                                   NULL, // userName
                                   NULL, // password
                                   0,    // options
                                   VIX_INVALID_HANDLE, // propertyListHandle
                                   NULL, // callbackProc
                                   NULL); // clientData

    if (VIX_OK != err) {
        std::cerr << "Failed to connect to VMware host: " << err << std::endl;
        return false;
    }

    return true;
}

std::vector<VMInfo> VMwareEngine::DiscoverVMs() {
    std::vector<VMInfo> vmList;
    
    // Method 1: Scan common VMware directories
    std::vector<std::string> searchPaths = {
        "C:\\Users\\<USER>\\Documents\\Shared Virtual Machines",
        "C:\\Virtual Machines",
        GetUserDocumentsPath() + "\\Virtual Machines"
    };

    for (const auto& path : searchPaths) {
        ScanDirectoryForVMs(path, vmList);
    }

    // Method 2: Check VMware Workstation registry for recent VMs
    ScanRegistryForVMs(vmList);

    // Method 3: Use VMware VIX API to enumerate running VMs
    EnumerateRunningVMs(vmList);

    return vmList;
}

bool VMwareEngine::StartVM(const std::string& vmxPath) {
    VixHandle jobHandle = VixHost_OpenVM(hostHandle,
                                         vmxPath.c_str(),
                                         VIX_VMOPEN_NORMAL,
                                         VIX_INVALID_HANDLE,
                                         NULL,
                                         NULL);

    VixError err = VixJob_Wait(jobHandle, VIX_PROPERTY_JOB_RESULT_HANDLE, &vmHandles[vmxPath], VIX_PROPERTY_NONE);
    Vix_ReleaseHandle(jobHandle);

    if (VIX_OK != err) {
        std::cerr << "Failed to open VM: " << vmxPath << " Error: " << err << std::endl;
        return false;
    }

    // Power on the VM
    jobHandle = VixVM_PowerOn(vmHandles[vmxPath],
                              VIX_VMPOWEROP_LAUNCH_GUI,
                              VIX_INVALID_HANDLE,
                              NULL,
                              NULL);

    err = VixJob_Wait(jobHandle, VIX_PROPERTY_NONE);
    Vix_ReleaseHandle(jobHandle);

    if (VIX_OK != err) {
        std::cerr << "Failed to power on VM: " << vmxPath << " Error: " << err << std::endl;
        return false;
    }

    std::cout << "Successfully started VM: " << vmxPath << std::endl;
    return true;
}

bool VMwareEngine::StopVM(const std::string& vmxPath) {
    auto it = vmHandles.find(vmxPath);
    if (it == vmHandles.end()) {
        std::cerr << "VM not found in handle map: " << vmxPath << std::endl;
        return false;
    }

    VixHandle jobHandle = VixVM_PowerOff(it->second,
                                         VIX_VMPOWEROP_NORMAL,
                                         NULL,
                                         NULL);

    VixError err = VixJob_Wait(jobHandle, VIX_PROPERTY_NONE);
    Vix_ReleaseHandle(jobHandle);

    if (VIX_OK != err) {
        std::cerr << "Failed to power off VM: " << vmxPath << " Error: " << err << std::endl;
        return false;
    }

    // Release the VM handle
    Vix_ReleaseHandle(it->second);
    vmHandles.erase(it);

    std::cout << "Successfully stopped VM: " << vmxPath << std::endl;
    return true;
}

bool VMwareEngine::PauseVM(const std::string& vmxPath) {
    auto it = vmHandles.find(vmxPath);
    if (it == vmHandles.end()) {
        return false;
    }

    VixHandle jobHandle = VixVM_Pause(it->second,
                                      0, // options
                                      VIX_INVALID_HANDLE,
                                      NULL,
                                      NULL);

    VixError err = VixJob_Wait(jobHandle, VIX_PROPERTY_NONE);
    Vix_ReleaseHandle(jobHandle);

    return (VIX_OK == err);
}

bool VMwareEngine::ResumeVM(const std::string& vmxPath) {
    auto it = vmHandles.find(vmxPath);
    if (it == vmHandles.end()) {
        return false;
    }

    VixHandle jobHandle = VixVM_Unpause(it->second,
                                        0, // options
                                        VIX_INVALID_HANDLE,
                                        NULL,
                                        NULL);

    VixError err = VixJob_Wait(jobHandle, VIX_PROPERTY_NONE);
    Vix_ReleaseHandle(jobHandle);

    return (VIX_OK == err);
}

VMStatus VMwareEngine::GetVMStatus(const std::string& vmxPath) {
    auto it = vmHandles.find(vmxPath);
    if (it == vmHandles.end()) {
        return VMStatus::Unknown;
    }

    int powerState;
    VixError err = Vix_GetProperties(it->second,
                                     VIX_PROPERTY_VM_POWER_STATE,
                                     &powerState,
                                     VIX_PROPERTY_NONE);

    if (VIX_OK != err) {
        return VMStatus::Unknown;
    }

    if (powerState & VIX_POWERSTATE_POWERED_ON) {
        return VMStatus::Running;
    } else if (powerState & VIX_POWERSTATE_SUSPENDED) {
        return VMStatus::Suspended;
    } else {
        return VMStatus::Stopped;
    }
}

bool VMwareEngine::ModifyVMConfig(const std::string& vmxPath, const ConfigChanges& changes) {
    // Read current VMX file
    std::ifstream file(vmxPath);
    if (!file.is_open()) {
        std::cerr << "Failed to open VMX file: " << vmxPath << std::endl;
        return false;
    }

    std::map<std::string, std::string> config;
    std::string line;
    
    while (std::getline(file, line)) {
        size_t pos = line.find('=');
        if (pos != std::string::npos) {
            std::string key = line.substr(0, pos);
            std::string value = line.substr(pos + 1);
            
            // Trim whitespace and quotes
            key.erase(0, key.find_first_not_of(" \t"));
            key.erase(key.find_last_not_of(" \t") + 1);
            value.erase(0, value.find_first_not_of(" \t\""));
            value.erase(value.find_last_not_of(" \t\"") + 1);
            
            config[key] = value;
        }
    }
    file.close();

    // Apply changes
    for (const auto& change : changes.modifications) {
        config[change.first] = change.second;
    }

    // Write back to file
    std::ofstream outFile(vmxPath);
    if (!outFile.is_open()) {
        std::cerr << "Failed to write VMX file: " << vmxPath << std::endl;
        return false;
    }

    for (const auto& entry : config) {
        outFile << entry.first << " = \"" << entry.second << "\"" << std::endl;
    }

    outFile.close();
    return true;
}

bool VMwareEngine::CreateSnapshot(const std::string& vmxPath, const std::string& snapshotName) {
    auto it = vmHandles.find(vmxPath);
    if (it == vmHandles.end()) {
        return false;
    }

    VixHandle jobHandle = VixVM_CreateSnapshot(it->second,
                                               snapshotName.c_str(),
                                               "Snapshot created by VMware Controller",
                                               VIX_SNAPSHOT_INCLUDE_MEMORY,
                                               VIX_INVALID_HANDLE,
                                               NULL,
                                               NULL);

    VixError err = VixJob_Wait(jobHandle, VIX_PROPERTY_NONE);
    Vix_ReleaseHandle(jobHandle);

    return (VIX_OK == err);
}

void VMwareEngine::ScanDirectoryForVMs(const std::string& directory, std::vector<VMInfo>& vmList) {
    if (!std::filesystem::exists(directory)) {
        return;
    }

    for (const auto& entry : std::filesystem::recursive_directory_iterator(directory)) {
        if (entry.is_regular_file() && entry.path().extension() == ".vmx") {
            VMInfo info;
            info.vmxPath = entry.path().string();
            info.name = entry.path().stem().string();
            info.status = VMStatus::Unknown;
            
            // Try to extract display name from VMX file
            ExtractVMDisplayName(info.vmxPath, info.displayName);
            
            vmList.push_back(info);
        }
    }
}

void VMwareEngine::ExtractVMDisplayName(const std::string& vmxPath, std::string& displayName) {
    std::ifstream file(vmxPath);
    std::string line;
    
    while (std::getline(file, line)) {
        if (line.find("displayName") != std::string::npos) {
            size_t pos = line.find('=');
            if (pos != std::string::npos) {
                displayName = line.substr(pos + 1);
                // Remove quotes and whitespace
                displayName.erase(0, displayName.find_first_not_of(" \t\""));
                displayName.erase(displayName.find_last_not_of(" \t\"") + 1);
                break;
            }
        }
    }
}

std::string VMwareEngine::GetUserDocumentsPath() {
    char path[MAX_PATH];
    if (SUCCEEDED(SHGetFolderPathA(NULL, CSIDL_MYDOCUMENTS, NULL, SHGFP_TYPE_CURRENT, path))) {
        return std::string(path);
    }
    return "";
}

void VMwareEngine::Cleanup() {
    // Release all VM handles
    for (auto& handle : vmHandles) {
        Vix_ReleaseHandle(handle.second);
    }
    vmHandles.clear();

    // Disconnect from host
    if (hostHandle != VIX_INVALID_HANDLE) {
        VixHost_Disconnect(hostHandle);
        hostHandle = VIX_INVALID_HANDLE;
    }
}
