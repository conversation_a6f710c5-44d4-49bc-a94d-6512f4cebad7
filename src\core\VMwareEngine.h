#pragma once

#include <string>
#include <vector>
#include <map>
#include <memory>
#include "vix.h"
#include <shlobj.h>

enum class VMStatus {
    Unknown,
    Stopped,
    Running,
    Suspended,
    Paused
};

struct VMInfo {
    std::string name;
    std::string displayName;
    std::string vmxPath;
    VMStatus status;
    std::string guestOS;
    int memoryMB;
    int numCPUs;
    std::string version;
    bool toolsInstalled;
    std::string ipAddress;
    
    VMInfo() : status(VMStatus::Unknown), memoryMB(0), numCPUs(0), toolsInstalled(false) {}
};

struct ConfigChanges {
    std::map<std::string, std::string> modifications;
    
    void AddChange(const std::string& key, const std::string& value) {
        modifications[key] = value;
    }
    
    // Predefined anti-detection configurations
    void ApplyAntiDetectionSettings() {
        // Hide VMware tools
        modifications["isolation.tools.unity.disable"] = "TRUE";
        modifications["isolation.tools.unityInterlockOperation.disable"] = "TRUE";
        modifications["isolation.tools.unity.taskbar.disable"] = "TRUE";
        modifications["isolation.tools.unityActive.disable"] = "TRUE";
        modifications["isolation.tools.unity.windowContents.disable"] = "TRUE";
        modifications["isolation.tools.unity.push.update.disable"] = "TRUE";
        
        // Disable VMware-specific features
        modifications["isolation.tools.ghi.autologon.disable"] = "TRUE";
        modifications["isolation.tools.ghi.launchmenu.change"] = "TRUE";
        modifications["isolation.tools.memSchedFakeSampleStats.disable"] = "TRUE";
        modifications["isolation.tools.getCreds.disable"] = "TRUE";
        
        // Hide hypervisor presence
        modifications["hypervisor.cpuid.v0"] = "FALSE";
        modifications["mce.enable"] = "TRUE";
        modifications["vhu.enable"] = "FALSE";
        
        // Modify hardware identifiers
        modifications["ethernet0.addressType"] = "static";
        modifications["ethernet0.address"] = GenerateRandomMAC();
        
        // SMBIOS modifications
        modifications["smbios.reflectHost"] = "TRUE";
        modifications["hw.model"] = "OptiPlex 7090";
        modifications["smbios.addHostVendor"] = "TRUE";
        
        // CPU modifications
        modifications["cpuid.80000001.edx"] = "0x20100800";
        modifications["cpuid.80000001.ecx"] = "0x01";
        modifications["cpuid.1.edx"] = "0x0fcbfbff";
        modifications["cpuid.1.ecx"] = "0xf7fa3203";
    }
    
private:
    std::string GenerateRandomMAC() {
        // Generate a random MAC address avoiding VMware OUIs
        // VMware OUIs: 00:0C:29, 00:50:56, 00:1C:14
        std::string mac = "02:"; // Locally administered unicast
        
        for (int i = 0; i < 5; i++) {
            if (i > 0) mac += ":";
            int byte = rand() % 256;
            char hex[3];
            sprintf_s(hex, "%02X", byte);
            mac += hex;
        }
        
        return mac;
    }
};

class VMwareEngine {
private:
    VixHandle hostHandle;
    std::map<std::string, VixHandle> vmHandles;
    
    // Helper methods
    void ScanDirectoryForVMs(const std::string& directory, std::vector<VMInfo>& vmList);
    void ScanRegistryForVMs(std::vector<VMInfo>& vmList);
    void EnumerateRunningVMs(std::vector<VMInfo>& vmList);
    void ExtractVMDisplayName(const std::string& vmxPath, std::string& displayName);
    std::string GetUserDocumentsPath();
    bool IsVMRunning(const std::string& vmxPath);
    
public:
    VMwareEngine();
    ~VMwareEngine();
    
    // Core functionality
    bool Initialize();
    void Cleanup();
    
    // VM Discovery and Management
    std::vector<VMInfo> DiscoverVMs();
    VMInfo GetVMInfo(const std::string& vmxPath);
    bool RefreshVMInfo(VMInfo& vmInfo);
    
    // VM Lifecycle Operations
    bool StartVM(const std::string& vmxPath);
    bool StopVM(const std::string& vmxPath);
    bool PauseVM(const std::string& vmxPath);
    bool ResumeVM(const std::string& vmxPath);
    bool ResetVM(const std::string& vmxPath);
    bool SuspendVM(const std::string& vmxPath);
    
    // VM Status and Monitoring
    VMStatus GetVMStatus(const std::string& vmxPath);
    bool IsVMPoweredOn(const std::string& vmxPath);
    std::string GetVMIPAddress(const std::string& vmxPath);
    bool WaitForVMTools(const std::string& vmxPath, int timeoutSeconds = 300);
    
    // Configuration Management
    bool ModifyVMConfig(const std::string& vmxPath, const ConfigChanges& changes);
    bool BackupVMConfig(const std::string& vmxPath, const std::string& backupPath);
    bool RestoreVMConfig(const std::string& vmxPath, const std::string& backupPath);
    bool ApplyAntiDetectionConfig(const std::string& vmxPath);
    
    // Snapshot Management
    bool CreateSnapshot(const std::string& vmxPath, const std::string& snapshotName);
    bool RestoreSnapshot(const std::string& vmxPath, const std::string& snapshotName);
    bool DeleteSnapshot(const std::string& vmxPath, const std::string& snapshotName);
    std::vector<std::string> ListSnapshots(const std::string& vmxPath);
    
    // Guest Operations (requires VMware Tools)
    bool RunProgramInGuest(const std::string& vmxPath, const std::string& programPath, 
                          const std::string& arguments = "", bool waitForCompletion = true);
    bool CopyFileToGuest(const std::string& vmxPath, const std::string& hostPath, 
                        const std::string& guestPath);
    bool CopyFileFromGuest(const std::string& vmxPath, const std::string& guestPath, 
                          const std::string& hostPath);
    bool LoginToGuest(const std::string& vmxPath, const std::string& username, 
                     const std::string& password);
    
    // Advanced Operations
    bool CloneVM(const std::string& sourceVmxPath, const std::string& destVmxPath, 
                bool linkedClone = false);
    bool DeleteVM(const std::string& vmxPath, bool deleteFiles = true);
    bool RegisterVM(const std::string& vmxPath);
    bool UnregisterVM(const std::string& vmxPath);
    
    // Resource Management
    struct ResourceUsage {
        double cpuUsagePercent;
        long memoryUsageMB;
        long diskUsageMB;
        std::string networkActivity;
    };
    
    ResourceUsage GetVMResourceUsage(const std::string& vmxPath);
    bool SetVMMemorySize(const std::string& vmxPath, int memoryMB);
    bool SetVMCPUCount(const std::string& vmxPath, int numCPUs);
    
    // Network Management
    bool AddNetworkAdapter(const std::string& vmxPath, const std::string& networkType);
    bool RemoveNetworkAdapter(const std::string& vmxPath, int adapterIndex);
    bool SetNetworkAdapterMAC(const std::string& vmxPath, int adapterIndex, const std::string& macAddress);
    
    // Error handling
    std::string GetLastError() const { return lastError; }
    
private:
    std::string lastError;
    void SetLastError(const std::string& error) { lastError = error; }
    
    // VMware process detection and management
    bool IsVMwareRunning();
    bool StartVMwareServices();
    bool StopVMwareServices();
    std::vector<DWORD> GetVMwareProcesses();
    
    // Registry operations
    bool ReadRegistryValue(HKEY hKey, const std::string& subKey, const std::string& valueName, std::string& value);
    bool WriteRegistryValue(HKEY hKey, const std::string& subKey, const std::string& valueName, const std::string& value);
    
    // File operations
    bool FileExists(const std::string& filePath);
    bool DirectoryExists(const std::string& dirPath);
    bool CreateDirectoryRecursive(const std::string& dirPath);
    
    // Utility functions
    std::string ToLower(const std::string& str);
    std::string Trim(const std::string& str);
    std::vector<std::string> Split(const std::string& str, char delimiter);
};
